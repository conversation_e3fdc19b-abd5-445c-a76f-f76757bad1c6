Server = .
ServerName = xbit-agent

# Define variables
GOOS = linux   # Target OS (can be overridden)
GOARCH = amd64 # Target architecture
BinDir = ./bin

# Detect local platform for development
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

build:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

build-local:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go
	@echo "Server built for $(LOCAL_GOOS)/$(LOCAL_GOARCH) in $(BinDir)"

db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash --dir file://migrations

db-fix-checksum:
	./scripts/fix-migration-checksum.sh

db-apply:
	./scripts/run.sh local migrate

db-apply-docker:
	./scripts/run.sh docker migrate

# Atlas migrations (for development only)
db-apply-atlas:
	atlas migrate apply --url "postgres://dev:dev@localhost:5432/dex_v2?sslmode=disable" --dir file://migrations

db-apply-atlas-docker:
	atlas migrate apply --url "postgres://postgres:postgres@localhost:5432/agent?sslmode=disable" --dir file://migrations

gqlgen:
	go run github.com/99designs/gqlgen generate --config gqlgen.yml

install-deps:
	go mod tidy

run:
	go run cmd/graphql/main.go

run-local:
	./scripts/run.sh local run

run-docker:
	docker-compose up -d

dev:
	./scripts/run.sh local dev

dev-air:
	air -c .air.toml

test:
	go test ./...

clean:
	rm -rf $(BinDir)

# NATS consumer management
cleanup-consumers:
	go run cmd/cleanup-consumers/main.go cleanup

list-consumers:
	go run cmd/cleanup-consumers/main.go list

delete-consumer:
	@if [ -z "$(CONSUMER)" ]; then \
		echo "Usage: make delete-consumer CONSUMER=<consumer-name>"; \
		echo "Example: make delete-consumer CONSUMER=xbit-agent-affiliate-tx-1753168518"; \
		exit 1; \
	fi
	go run cmd/cleanup-consumers/main.go delete $(CONSUMER)

.PHONY: build build-local db-diff db-rehash db-fix-checksum db-apply db-apply-docker db-apply-atlas db-apply-atlas-docker gqlgen install-deps run dev test clean cleanup-consumers list-consumers delete-consumer
